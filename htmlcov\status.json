{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "381ef7bb76795fe0e4bf1ead21db9abb", "files": {"z_5f5ca9ffd734e917___init___py": {"hash": "7dd9ff0ed1c31321c0ff2f0c6e68f4db", "index": {"url": "z_5f5ca9ffd734e917___init___py.html", "file": "src\\time_series_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5ca9ffd734e917_api_py": {"hash": "45b4c111b401aafee58e0858edb47e75", "index": {"url": "z_5f5ca9ffd734e917_api_py.html", "file": "src\\time_series_analyzer\\api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5ca9ffd734e917_cli_py": {"hash": "2a5e98057d01fbda0bbd665415d2e39d", "index": {"url": "z_5f5ca9ffd734e917_cli_py.html", "file": "src\\time_series_analyzer\\cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5ca9ffd734e917_formatters_py": {"hash": "7de4bea73d18c15d8451455bbba02c2a", "index": {"url": "z_5f5ca9ffd734e917_formatters_py.html", "file": "src\\time_series_analyzer\\formatters.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 223, "n_excluded": 0, "n_missing": 206, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5ca9ffd734e917_models_py": {"hash": "e03f72919c3af08125744e19758f9f31", "index": {"url": "z_5f5ca9ffd734e917_models_py.html", "file": "src\\time_series_analyzer\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5ca9ffd734e917_parsers_py": {"hash": "59cf5b0565eb0085fdff6bf671fd9851", "index": {"url": "z_5f5ca9ffd734e917_parsers_py.html", "file": "src\\time_series_analyzer\\parsers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5ca9ffd734e917_transfer_function_py": {"hash": "ce82c52dd3c09c1755c81c1f3f036826", "index": {"url": "z_5f5ca9ffd734e917_transfer_function_py.html", "file": "src\\time_series_analyzer\\transfer_function.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}