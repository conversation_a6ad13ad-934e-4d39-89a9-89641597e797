<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 10:26 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917___init___py.html">src\time_series_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t23">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t23"><data value='init__'>TimeSeriesAnalyzer.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t34">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t34"><data value='create_arima_model'>TimeSeriesAnalyzer.create_arima_model</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t62">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t62"><data value='create_sarima_model'>TimeSeriesAnalyzer.create_sarima_model</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t97">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t97"><data value='parse_model_string'>TimeSeriesAnalyzer.parse_model_string</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t109">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t109"><data value='load_model_from_file'>TimeSeriesAnalyzer.load_model_from_file</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t121">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t121"><data value='derive_transfer_function'>TimeSeriesAnalyzer.derive_transfer_function</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t133">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t133"><data value='analyze_stability'>TimeSeriesAnalyzer.analyze_stability</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t145">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t145"><data value='compute_impulse_response'>TimeSeriesAnalyzer.compute_impulse_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t159">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t159"><data value='compute_frequency_response'>TimeSeriesAnalyzer.compute_frequency_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t173">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t173"><data value='generate_report'>TimeSeriesAnalyzer.generate_report</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t214">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t214"><data value='quick_analyze'>TimeSeriesAnalyzer.quick_analyze</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t280">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t280"><data value='analyze_arima'>analyze_arima</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t305">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t305"><data value='analyze_sarima'>analyze_sarima</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t338">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html#t338"><data value='parse_and_analyze'>parse_and_analyze</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html">src\time_series_analyzer\api.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t20">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t20"><data value='main'>main</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t45">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t45"><data value='analyze'>analyze</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t109">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t109"><data value='impulse'>impulse</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t158">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t158"><data value='frequency'>frequency</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t216">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t216"><data value='stability'>stability</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t266">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html#t266"><data value='examples'>examples</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html">src\time_series_analyzer\cli.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t21">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t21"><data value='init__'>OutputFormatter.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t30">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t30"><data value='format_latex'>OutputFormatter.format_latex</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t88">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t88"><data value='format_arima_latex'>OutputFormatter._format_arima_latex</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t144">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t144"><data value='format_sarima_latex'>OutputFormatter._format_sarima_latex</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t195">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t195"><data value='polynomial_to_latex'>OutputFormatter._polynomial_to_latex</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t202">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t202"><data value='format_transfer_function_latex'>OutputFormatter._format_transfer_function_latex</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t238">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t238"><data value='format_stability_latex'>OutputFormatter._format_stability_latex</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t255">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t255"><data value='format_plain_text'>OutputFormatter.format_plain_text</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t361">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html#t361"><data value='format_json'>OutputFormatter.format_json</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html">src\time_series_analyzer\formatters.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t49">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t49"><data value='validate_ar_params'>ARIMAModel.validate_ar_params</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t59">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t59"><data value='validate_ma_params'>ARIMAModel.validate_ma_params</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t69">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t69"><data value='validate_model'>ARIMAModel.validate_model</data></a></td>
                <td>11</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="4 11">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t90">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t90"><data value='get_ar_polynomial'>ARIMAModel.get_ar_polynomial</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t112">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t112"><data value='get_ma_polynomial'>ARIMAModel.get_ma_polynomial</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t134">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t134"><data value='get_difference_polynomial'>ARIMAModel.get_difference_polynomial</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t159">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t159"><data value='to_dict'>ARIMAModel.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t174">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t174"><data value='str__'>ARIMAModel.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t204">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t204"><data value='validate_seasonal_ar_params'>SeasonalARIMAModel.validate_seasonal_ar_params</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t214">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t214"><data value='validate_seasonal_ma_params'>SeasonalARIMAModel.validate_seasonal_ma_params</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t224">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t224"><data value='validate_seasonal_model'>SeasonalARIMAModel.validate_seasonal_model</data></a></td>
                <td>15</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t253">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t253"><data value='get_seasonal_ar_polynomial'>SeasonalARIMAModel.get_seasonal_ar_polynomial</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t276">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t276"><data value='get_seasonal_ma_polynomial'>SeasonalARIMAModel.get_seasonal_ma_polynomial</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t299">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t299"><data value='get_seasonal_difference_polynomial'>SeasonalARIMAModel.get_seasonal_difference_polynomial</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t320">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html#t320"><data value='to_dict'>SeasonalARIMAModel.to_dict</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html">src\time_series_analyzer\models.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="47 47">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t20">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t20"><data value='parse_arima_string'>ModelParser.parse_arima_string</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t80">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t80"><data value='parse_json_file'>ModelParser.parse_json_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t101">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t101"><data value='parse_yaml_file'>ModelParser.parse_yaml_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t122">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t122"><data value='validate_config_data'>ModelParser._validate_config_data</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t143">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t143"><data value='interactive_input'>ModelParser.interactive_input</data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t246">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t246"><data value='create_model_from_dict'>ModelParser.create_model_from_dict</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t267">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t267"><data value='parse_from_string'>ModelParser.parse_from_string</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t281">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t281"><data value='parse_from_file'>ModelParser.parse_from_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t303">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html#t303"><data value='parse_interactive'>ModelParser.parse_interactive</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html">src\time_series_analyzer\parsers.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t24">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t24"><data value='init__'>TransferFunction.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t43">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t43"><data value='simplify'>TransferFunction._simplify</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t63">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t63"><data value='evaluate_at_frequency'>TransferFunction.evaluate_at_frequency</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t81">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t81"><data value='get_poles'>TransferFunction.get_poles</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t89">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t89"><data value='get_zeros'>TransferFunction.get_zeros</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t97">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t97"><data value='is_stable'>TransferFunction.is_stable</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t105">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t105"><data value='str__'>TransferFunction.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t109">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t109"><data value='repr__'>TransferFunction.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t120">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t120"><data value='init__'>TransferFunctionDeriver.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t131">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t131"><data value='derive_arima_transfer_function'>TransferFunctionDeriver.derive_arima_transfer_function</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t159">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t159"><data value='derive_sarima_transfer_function'>TransferFunctionDeriver.derive_sarima_transfer_function</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t192">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t192"><data value='derive_transfer_function'>TransferFunctionDeriver.derive_transfer_function</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t209">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t209"><data value='derive_impulse_response'>TransferFunctionDeriver.derive_impulse_response</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t247">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t247"><data value='analyze_stability'>TransferFunctionDeriver.analyze_stability</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t275">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html#t275"><data value='get_frequency_response'>TransferFunctionDeriver.get_frequency_response</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html">src\time_series_analyzer\transfer_function.py</a></td>
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>868</td>
                <td>713</td>
                <td>0</td>
                <td class="right" data-ratio="155 868">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 10:26 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
