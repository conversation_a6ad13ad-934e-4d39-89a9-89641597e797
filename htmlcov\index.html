<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 10:26 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917___init___py.html">src\time_series_analyzer\__init__.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_api_py.html">src\time_series_analyzer\api.py</a></td>
                <td>64</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="21 64">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_cli_py.html">src\time_series_analyzer\cli.py</a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_formatters_py.html">src\time_series_analyzer\formatters.py</a></td>
                <td>223</td>
                <td>206</td>
                <td>0</td>
                <td class="right" data-ratio="17 223">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_models_py.html">src\time_series_analyzer\models.py</a></td>
                <td>157</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="61 157">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_parsers_py.html">src\time_series_analyzer\parsers.py</a></td>
                <td>165</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="25 165">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5ca9ffd734e917_transfer_function_py.html">src\time_series_analyzer\transfer_function.py</a></td>
                <td>110</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="22 110">20%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>868</td>
                <td>713</td>
                <td>0</td>
                <td class="right" data-ratio="155 868">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 10:26 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_5f5ca9ffd734e917_transfer_function_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_5f5ca9ffd734e917___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
