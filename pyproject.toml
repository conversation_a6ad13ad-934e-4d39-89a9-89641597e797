[project]
name = "time-series-model-transfer-function-analyzer"
version = "0.1.0"
description = "时序模型传递函数分析器 - 自动推导ARIMA模型的传递函数表达式"
readme = "README.md"
requires-python = ">=3.9"
authors = [
    {name = "zym", email = "<EMAIL>"}
]
keywords = ["time-series", "arima", "transfer-function", "econometrics", "signal-processing"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
dependencies = [
    "sympy>=1.12",
    "click>=8.0",
    "pydantic>=2.0",
    "pyyaml>=6.0",
    "rich>=13.0",
    "numpy>=1.24",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "black>=23.0",
    "isort>=5.12",
    "flake8>=6.0",
    "mypy>=1.0",
]
docs = [
    "sphinx>=7.0",
    "sphinx-rtd-theme>=1.3",
    "myst-parser>=2.0",
]

[project.scripts]
tsm-analyzer = "time_series_analyzer.cli:main"

[project.urls]
Homepage = "https://github.com/zym9863/time-series-model-transfer-function-analyzer"
Repository = "https://github.com/zym9863/time-series-model-transfer-function-analyzer"
Documentation = "https://time-series-model-transfer-function-analyzer.readthedocs.io"
Issues = "https://github.com/zym9863/time-series-model-transfer-function-analyzer/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/time_series_analyzer"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=time_series_analyzer --cov-report=html --cov-report=term-missing"

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-cov>=6.2.1",
]
